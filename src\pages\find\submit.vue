<template>
  <div class="w-full" id="submit-page">
    <div class="w-[1280px] mx-auto">
      <search-step-card :currentStep="1"></search-step-card>
    </div>
    <div class="bg-[#F2F2F2]">
      <div class="w-[1280px] mx-auto flex py-[12px] px-[20px]">
        <div class="flex-1 mr-[18px]">
          <n-form
            :rules="rules"
            ref="submitFromRef"
            label-placement="left"
            class="pt-[28px] pb-[38px] px-[20px] bg-white rounded-[8px]"
            :model="pageData.submitForm"
            label-width="170px"
          >
            <n-form-item
              path="submitName"
              class="input-form-item strong-item"
              :label="authStore.i18n('cm_submit.username')"
            >
              <n-input
                v-trim
                clearable
                :bordered="false"
                @keydown.enter.prevent
                v-model:value="pageData.submitForm.submitName"
                :placeholder="authStore.i18n('cm_submit.usernamePlaceholder')"
              />
            </n-form-item>
            <n-form-item
              path="countryId"
              class="input-form-item strong-item"
              :label="authStore.i18n('cm_submit.country')"
            >
              <n-select
                filterable
                :bordered="false"
                value-field="id"
                label-field="countryEsName"
                :options="pageData.countryList"
                v-model:value="pageData.submitForm.countryId"
                @update:value="
                  (value, option) => onSelectCountry(value, option)
                "
                :placeholder="authStore.i18n('cm_submit.countryPlaceholder')"
              />
            </n-form-item>
            <n-form-item
              path="whatsapp"
              class="input-form-item strong-item"
              label="WhatsApp"
            >
              <n-input-group class="relative">
                <n-input
                  v-trim
                  readonly
                  class="!w-[56px]"
                  :bordered="false"
                  @keydown.enter.prevent
                  v-model:value="pageData.submitForm.areaCode"
                  placeholder="+000"
                />
                <div
                  class="w-[1px] h-[14px] bg-[#e6e6e6] absolute left-[40px] top-[30%] translate-y-[-50%]"
                ></div>
                <n-input
                  v-trim
                  clearable
                  :bordered="false"
                  @keydown.enter.prevent
                  v-model:value="pageData.submitForm.whatsapp"
                  :placeholder="authStore.i18n('cm_search.pleaseInputWhatsapp')"
                />
              </n-input-group>
            </n-form-item>
            <n-form-item
              path="email"
              class="input-form-item strong-item"
              :label="authStore.i18n('cm_search.email')"
            >
              <n-input
                v-trim
                clearable
                :bordered="false"
                @keydown.enter.prevent
                v-model:value="pageData.submitForm.email"
                :placeholder="authStore.i18n('cm_search.pleaseInputEmail')"
              />
            </n-form-item>
            <n-form-item
              path="postcode"
              class="input-form-item mt-[15px]"
              :label="authStore.i18n('cm_search.postcode')"
            >
              <n-input
                v-trim
                clearable
                :bordered="false"
                :maxlength="20"
                @keydown.enter.prevent
                v-model:value="pageData.submitForm.postcode"
                :placeholder="authStore.i18n('cm_search.pleaseInputPostcode')"
              />
            </n-form-item>
            <n-form-item
              path="address"
              class="input-form-item"
              :label="authStore.i18n('cm_search.address')"
            >
              <n-input
                v-trim
                clearable
                :bordered="false"
                @keydown.enter.prevent
                v-model:value="pageData.submitForm.address"
                :placeholder="authStore.i18n('cm_search.pleaseInputAddress')"
              />
            </n-form-item>
            <n-form-item
              path="remark"
              class="mt-[10px]"
              :label="authStore.i18n('cm_submit.requirementDescription')"
            >
              <n-input
                v-trim
                clearable
                v-model:value="pageData.submitForm.remark"
                type="textarea"
                :autosize="{
                  minRows: 1,
                  maxRows: 5,
                }"
                :placeholder="
                  authStore.i18n('cm_submit.requirementPlaceholder')
                "
                class="min-h-[40px] rounded-[12px]"
              />
            </n-form-item>
          </n-form>
          <div v-if="pageData.stat" class="bg-white mt-[4px] rounded-[8px]">
            <n-collapse :show-arrow="false">
              <n-collapse-item
                v-for="(goods, index) in pageData.goodsList"
                :key="goods.goodsId"
                class="ml-2 p-[20px]"
              >
                <template #header>
                  <goods-card
                    :goods="goods"
                    :from="'submit'"
                    spmCode="checkout-goods-list"
                    :spmIndex="index"
                  ></goods-card>
                </template>
                <template #header-extra="{ collapsed }">
                  <icon-card
                    v-if="collapsed"
                    name="uil:angle-down"
                    color="#797979"
                    size="32"
                  ></icon-card>
                  <icon-card
                    v-else
                    name="uil:angle-up"
                    color="#797979"
                    size="32"
                  ></icon-card>
                </template>
                <template #arrow><span class="hidden"></span></template>
                <!-- 三级分类 -->
                <div
                  v-for="sku in goods.skuList"
                  :key="sku.skuId"
                  class="ml-8 sku-checkbox"
                >
                  <sku-card
                    :sku="sku"
                    :goods="goods"
                    :disabledInput="true"
                    class="mb-4"
                  ></sku-card>
                </div>
              </n-collapse-item>
            </n-collapse>
          </div>
        </div>
        <div class="w-[400px]">
          <n-affix :trigger-top="20" id="submit-affix" class="h-fit">
            <div
              class="w-[400px] flex flex-col gap-[24px] px-[20px] py-[24px] rounded-[8px] bg-white"
            >
              <div
                class="text-[18px] leading-[18px] font-medium text-[#1A1A1A]"
              >
                {{ authStore.i18n("cm_find_inquireSummary") }}
              </div>
              <div class="flex justify-between text-[16px] leading-[16px]">
                <span>{{ authStore.i18n("cm_find_quantityOfModels") }}</span>
                <span>
                  <span class="font-medium">{{
                    pageData.stat?.goodsCount
                  }}</span>
                  {{
                    pageData.stat?.goodsCount > 1
                      ? authStore.i18n("cm_find_productModels")
                      : authStore.i18n("cm_find_productModel")
                  }}
                </span>
              </div>
              <div class="flex justify-between text-[16px] leading-[16px]">
                <span>{{ authStore.i18n("cm_find_quantityOfUnits") }}</span>
                <span>
                  <span class="font-medium">{{
                    pageData.stat?.selectSkuTotalQuantity
                  }}</span>
                  {{
                    pageData.stat?.selectSkuTotalQuantity > 1
                      ? authStore.i18n("cm_find_totalSkuUnits")
                      : authStore.i18n("cm_find_totalSkuUnit")
                  }}
                </span>
              </div>
              <div class="flex justify-between text-[16px] leading-[16px]">
                <span>{{ authStore.i18n("cm_find_itemsCost") }}:</span
                ><span
                  class="text-[#e50113] font-medium"
                  v-if="
                    pageData.stat?.selectTotalSalePrice ||
                    pageData.stat?.selectTotalSalePrice === 0
                  "
                  >{{ setUnit(pageData.stat?.selectTotalSalePrice) }}</span
                >
              </div>
              <div
                class="flex justify-between text-[16px] leading-[16px] text-[#4D4D4D]"
              >
                <n-popover trigger="hover" raw>
                  <template #trigger>
                    <div class="flex items-center cursor-pointer">
                      <img
                        class="w-[14px] h-[14px] mr-[2px]"
                        src="@/assets/icons/common/alert-circle.svg"
                        :alt="authStore.i18n('cm_goods.estimatedShippingCost')"
                        referrerpolicy="no-referrer"
                      />
                      {{ authStore.i18n("cm_goods.estimatedShippingCost") }}:
                    </div>
                  </template>
                  <div
                    style="
                      z-index: 1;
                      width: 300px;
                      padding: 6px 14px;
                      background-color: #fff4d4;
                      transform-origin: inherit;
                      border: 1px solid #f7ba2a;
                    "
                  >
                    {{ authStore.i18n("cm_goods.freightAdjustmentPending") }}
                  </div>
                </n-popover>

                <span v-if="pageData?.totalEstimateFreight">{{
                  setUnit(pageData.totalEstimateFreight)
                }}</span>
                <span v-else>{{
                  authStore.i18n("cm_goods.pendingConfirmation")
                }}</span>
              </div>
            </div>
            <div class="w-[400px] mt-[16px]">
              <div
                class="text-[14px] leading-[14px] text-[#A6A6A6] text-center"
              >
                {{ authStore.i18n("cm_find_confirmWithoutPay") }}
              </div>

              <n-button
                size="large"
                color="#E50113"
                text-color="#fff"
                @click="onSubmit($event)"
                data-spm-box="checkout-to-order"
                class="rounded-[6px] w-full h-[44px] my-[12px]"
              >
                <img
                  alt="list"
                  loading="lazy"
                  src="@/assets/icons/common/cart-submit-white.svg"
                  class="w-[24px] h-[24px] mr-[8px]"
                  referrerpolicy="no-referrer"
                />
                <span class="text-[16px] leading-[16px]">{{
                  authStore.i18n("cm_submit.sendInquire")
                }}</span>
              </n-button>
              <div
                class="text-[14px] leading-[16px] text-[#A6A6A6] text-center"
              >
                {{ authStore.i18n("cm_submit.messageTitle") }}
              </div>
            </div>
          </n-affix>
        </div>

        <!-- <div class="flex gap-[20px] pb-[80px]">
        <div class="w-[740px]">
          <div class="mb-2" v-if="pageData.stat">
            {{ authStore.i18n("cm_submit.sendProduct") }}
            <span class="primary font-medium mx-2">{{
              pageData.stat?.goodsCount
            }}</span>
            {{ authStore.i18n("cm_submit.product")
            }}<span class="primary font-medium mx-3">
              {{ pageData.stat?.selectSkuTotalQuantity }}
            </span>
            {{ authStore.i18n("cm_submit.productItem") }}
            <span
              class="primary font-medium ml-2"
              v-if="pageData?.stat?.totalSalePrice"
              >{{ setUnit(pageData?.stat?.totalSalePrice) }} </span
            >.
            <span class="ml-2">
              <n-popover trigger="hover" raw>
                <template #trigger>
                  <span class="cursor-pointer mr-[4px]">
                    <icon-card
                      size="16"
                      name="mingcute:warning-line"
                      color="#F7BA2A"
                    ></icon-card>
                    {{ authStore.i18n("cm_goods.estimatedShippingCost") }}:
                  </span>
                </template>
                <div
                  style="
                    z-index: 1;
                    width: 320px;
                    padding: 6px 14px;
                    background-color: #fff4d4;
                    transform-origin: inherit;
                    border: 1px solid #f7ba2a;
                  "
                >
                  {{ authStore.i18n("cm_goods.freightAdjustmentPending") }}
                </div>
              </n-popover>

              <span
                v-if="pageData?.totalEstimateFreight"
                class="text-[#e50113] font-bold"
                >{{ setUnit(pageData.totalEstimateFreight) }}</span
              >
              <span v-else class="text-[#e50113]">{{
                authStore.i18n("cm_goods.pendingConfirmation")
              }}</span>
            </span>
          </div>
        </div>

        <div class="w-[520px] flex-shrink-0">
          <div class="text-base font-medium mb-[4px]">
            ¡Solo un último paso!
            <icon-card
              size="26"
              color="#e50113"
              name="fluent:clipboard-text-edit-24-regular"
            ></icon-card>
            Rellene los siguientes datos y haga clic al botón rojo para enviar
            su pedido.
          </div>
          <n-affix
            :trigger-top="20"
            id="form-affix"
            class="w-[520px] bg-[#f2f2f2] border-1 border-[#797979] rounded-[8px] relative z-10"
            style="
              height: calc(100vh - 55px);
              max-height: 780px;
              padding-bottom: 55px;
            "
          >
            <n-scrollbar y-scrollable class="py-[16px] px-[12px]">
              <div class="text-[14px] text-gray-600">
                {{ authStore.i18n("cm_submit.messageTitle") }}
              </div>
            </n-scrollbar>
            <div
              class="absolute bottom-0 left-0 right-0 p-[12px] bg-[#fff] rounded-[8px]"
            >
              <n-button
                block
                color="#E50113"
                @click="onSubmit($event)"
                class="px-8 py-[22px] rounded-[10px]"
                data-spm-box="checkout-to-order"
              >
                <icon-card
                  name="mdi:invoice-text-send-outline"
                  size="28"
                  class="mr-1"
                />
                <span class="text-[18px] leading-[18px] font-medium">{{
                  authStore.i18n("cm_submit.sendInquire")
                }}</span>
              </n-button>
            </div>
          </n-affix>
        </div>
      </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import type { FormInst, FormItemRule, FormRules } from "naive-ui";

import SkuCard from "./components/SkuCard.vue";
import GoodsCard from "./components/GoodsCard.vue";
import SearchStepCard from "./components/SearchStepCard.vue";

const route = useRoute();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const pageData = reactive<any>({
  stat: <any>{},
  countryRegexes: <any>{},
  goodsList: <any>[],
  countryList: <any>[],
  submitForm: <any>{
    submitName: "",
    whatsapp: "",
    email: "",
    remark: "",
    isTemporary: true,
    areaCode: "",
    countryId: null,
    postcode: "",
    address: "",
  },
  isHaveSubmitted: false,
  isSubmitLoading: false,
  defaultCountry: <any>{},
});
const submitFromRef = ref<FormInst | null>(null);
const userInfo = ref<object>({});
userInfo.value = config.public.userInfo as object;

if (isEmptyObject(userInfo.value)) {
  navigateTo("/");
}

await onGetInquiry();
await onGetCountry();

const rules: FormRules = {
  submitName: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_submit.usernamePlaceholder"),
    validator(rule: FormItemRule, value: any) {
      let remark;
      if (!value) {
        remark = `姓名：${value};报错提示：${authStore.i18n(
          "cm_submit.usernamePlaceholder"
        )}`;
        window?.MyStat?.addPageEvent("submit_name", remark);
        return false;
      } else {
        remark = `姓名：${value};`;
        window?.MyStat?.addPageEvent("submit_name", remark);
        return true;
      }
    },
  },
  countryId: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_submit.countryPlaceholder"),
    validator(rule: FormItemRule, value: any) {
      let remark;
      if (!value) {
        remark = `国家：${
          pageData.countryRegexes.countryCnName
        };报错提示：${authStore.i18n("cm_submit.countryPlaceholder")}`;
        window?.MyStat?.addPageEvent("submit_country", remark);
        return false;
      } else {
        remark = `国家：${pageData.countryRegexes?.countryCnName};（默认：${
          pageData.defaultCountry?.countryCnName || "无"
        }）`;
        window?.MyStat?.addPageEvent("submit_country", remark);
        return true;
      }
    },
  },
  whatsapp: {
    required: true,
    trigger: "blur",
    message: (() => {
      const phoneCountMessage = pageData.countryRegexes?.phoneCount
        ? `${authStore.i18n("cm_submit.whatsappTips")} ${
            pageData.countryRegexes?.phoneCount
          } ${authStore.i18n("cm_submit.whatsapp")}`
        : authStore.i18n("cm_submit.whatsappRequired");
      return `${phoneCountMessage}`;
    })(),
    validator(rule: FormItemRule, value: any) {
      let remark;
      const lengths =
        pageData.countryRegexes?.phoneCount &&
        pageData.countryRegexes.phoneCount.split(",").map(Number);
      if (value && value.length && lengths && lengths.length > 0) {
        for (const length of lengths) {
          // 如果匹配任何一个长度，返回 true
          if (value.length === length) {
            remark = `电话：${value};`;
            window?.MyStat?.addPageEvent("submit_phone", remark);
            return true;
          }
        }
      } else {
        if (value) {
          remark = `电话：${value};`;
          window?.MyStat?.addPageEvent("submit_phone", remark);
          return true;
        }
      }
      remark = `电话：${value};报错提示：${authStore.i18n(
        "cm_submit.whatsappTips"
      )} ${pageData.countryRegexes?.phoneCount} ${authStore.i18n(
        "cm_submit.whatsapp"
      )}`;
      window?.MyStat?.addPageEvent("submit_phone", remark);
      return false;
    },
  },
  email: {
    required: true,
    trigger: "blur",
    message: authStore.i18n("cm_search.emailTips"),
    validator(rule: FormItemRule, value: any) {
      let remark;
      const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      remark = `邮箱：${value}`;
      if (!pattern.test(value)) {
        remark = `邮箱：${value};报错提示：${authStore.i18n(
          "cm_search.emailTips"
        )}`;
      }
      window?.MyStat?.addPageEvent("submit_email", remark);
      return pattern.test(value);
    },
  },
};

async function onGetInquiry() {
  const inquiryInfo = authStore.getInquiryInfo();
  pageData.goodsList = inquiryInfo.goodsList;
  pageData.totalEstimateFreight = inquiryInfo?.totalEstimateFreight;
  pageData.stat = inquiryInfo.stat;
  if (inquiryInfo?.lastInquiry && !isEmptyObject(inquiryInfo?.lastInquiry)) {
    const {
      submitName,
      countryId,
      areaCode,
      whatsapp,
      email,
      postcode,
      address,
    } = inquiryInfo.lastInquiry;
    pageData.submitForm.submitName = submitName;
    pageData.submitForm.countryId = countryId;
    pageData.submitForm.areaCode = areaCode;
    pageData.submitForm.whatsapp = whatsapp;
    pageData.submitForm.email = email;
    pageData.submitForm.postcode = postcode;
    pageData.submitForm.address = address;
  } else {
    pageData.submitForm.email = userInfo?.value?.username;
  }
}

async function onGetCountry() {
  const res: any = await useGetCountry({});
  if (res?.result?.code === 200) {
    pageData.countryList = res?.data;
    // 优先使用用户上次询盘的国家
    if (pageData.submitForm.countryId) {
      res?.data.map((country: any) => {
        if (country.id === pageData.submitForm.countryId) {
          pageData.submitForm.areaCode = country?.areaCode;
          pageData.countryRegexes = country;
          pageData.defaultCountry = country;
        }
      });
    } else {
      // 用户没有上次询盘信息，则使用基于IP地址定位所属国家
      if (config.public.defaultCountryCode) {
        res?.data.map((country: any) => {
          if (country.countryCodeTwo === config.public.defaultCountryCode) {
            pageData.submitForm.countryId = country.id;
            pageData.submitForm.areaCode = country?.areaCode;
            pageData.countryRegexes = country;
            pageData.defaultCountry = country;
          }
        });
      }
    }
  }
}

function onSelectCountry(value: any, country: any) {
  pageData.submitForm.areaCode = country?.areaCode;
  pageData.countryRegexes = country;
  // 如果有长度校验 则校验长度
  if (pageData.countryRegexes?.phoneCount) {
    rules["whatsapp"].message = `${authStore.i18n("cm_submit.whatsappTips")} ${
      pageData.countryRegexes.phoneCount
    } ${authStore.i18n("cm_submit.whatsapp")}`;
  } else {
    // 没有长度校验 校验必填
    rules["whatsapp"].message = `${authStore.i18n(
      "cm_submit.whatsappRequired"
    )}`;
  }
}

async function onSubmit(event: any) {
  await submitFromRef.value?.validate();
  if (pageData.isSubmitLoading) return;
  const params = <any>[];
  pageData.goodsList.forEach((goods: any) => {
    goods.skuList.forEach((sku: any) => {
      params.push({
        quantity: sku.buyQty,
        skuId: sku.skuId,
        spm: sku.spm,
        routeId: goods.routeId,
        padc: sku.padc,
      });
    });
  });
  const {
    submitName,
    countryId,
    whatsapp,
    email,
    remark,
    areaCode,
    postcode,
    address,
  } = pageData.submitForm;
  let paramsObj = {
    submitName: submitName?.trim(),
    countryId: countryId?.trim(),
    whatsapp: whatsapp?.trim(),
    email: email?.trim(),
    remark: remark?.trim(),
    isTemporary: false,
    areaCode,
    params,
    fromCart: route.query.fromCart == "false" ? false : true, //不从购物车过来 需要传给服务端false
    postcode,
    address,
    siteId: window?.siteData?.siteInfo?.id,
  };

  pageData.isSubmitLoading = true;
  try {
    const res: any = await useSubmitInquiry(paramsObj);
    if (res?.result?.code === 200) {
      pageData.isHaveSubmitted = true;
      authStore.getCartList();
      if (!!window?.fbq) {
        window?.fbq("track", "Purchase", {
          content_type: "product",
          currency: "USD",
          value: pageData?.stat?.totalSalePrice,
          contents: pageData.goodsList,
        });
      }
      if (!!window?.ttq) {
        window?.ttq?.track("SubmitForm", {
          content_type: "product",
          currency: "USD",
          value: pageData?.stat?.totalSalePrice,
          contents: pageData.goodsList,
        });
      }
      const url = navigateUrl(
        `/find/submit-thankyou`,
        {
          email: email?.trim(),
          whatsapp: areaCode + whatsapp,
          firstSubmit: res?.data?.isFirstSubmit || false,
        },
        event
      );
      setTimeout(() => {
        window.location.replace(url);
      }, 200);
    } else {
      window?.MyStat?.addPageEvent(
        "submit_looking_error",
        `询盘提交错误:${res?.result?.message}`
      );
      showToast(res?.result?.message);
      pageData.isSubmitLoading = false;
    }
  } catch (error) {
    pageData.isSubmitLoading = false;
  }
}

onBeforeMount(() => {
  window.scrollTo({
    top: 0,
    left: 0,
    behavior: "auto",
  });
});

onMounted(() => {
  window.addEventListener("scroll", handleAnchorScroll);
});

onBeforeUnmount(() => {
  window.removeEventListener("scroll", handleAnchorScroll);
  !pageData.isHaveSubmitted && useSubmitTemporary(pageData.submitForm);
});

function handleAnchorScroll() {
  // 获取各个元素
  const footerElement = document.getElementById("page-footer");
  const formAffix = document.getElementById("submit-affix");

  if (footerElement && formAffix) {
    // 获取表单和页脚的高度
    const affixHeight = formAffix.offsetHeight;
    const footerHeight = footerElement.offsetHeight;
    // 判断文档高度是否小于(表单高度+页脚高度+80px误差)
    const criticalHeight = affixHeight + footerHeight + 80;
    // 文档高度足够，只需处理正常的固钉逻辑
    if (window.innerHeight >= criticalHeight) {
      formAffix.style.top = "20px";
    } else {
      const footerElement = document.getElementById("page-footer");
      const formAffix = document.getElementById("submit-affix");
      if (footerElement && formAffix) {
        const footerTop =
          footerElement.getBoundingClientRect().top + window.scrollY;
        const scrollTop =
          window.scrollY ||
          document.documentElement.scrollTop ||
          document.body.scrollTop ||
          0;
        const windowHeight =
          window.innerHeight ||
          document.documentElement.clientHeight ||
          document.body.clientHeight;

        // 计算距离底部的距离
        const distanceFromFooter = footerTop - (scrollTop + windowHeight);

        // 判断是否滚动到page-footer元素的位置
        if (distanceFromFooter <= 0) {
          // 计算right-affix的top值
          formAffix.style.top = distanceFromFooter + "px";
        } else {
          // 如果没有滚动到footer的位置，保持form-affix的初始top值
          formAffix.style.top = 20 + "px";
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: auto;
  margin: 0 auto;
  padding-top: 0rem;
  overflow-wrap: break-word;
  min-height: 70vh;
}
:deep(.n-card__content) {
  padding: 0 !important;
  padding-top: 16px !important;
}
:deep(.n-collapse .n-collapse-item) {
  margin: 0;
}
// :deep(.n-affix) {
//   height: 100%;
// }
.no-border-input .n-input__input,
.no-border-input.n-input input,
.no-border-input.n-input__textarea {
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
}

.no-border-input .n-base-selection {
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
}

:deep(.n-form-item) {
  --n-blank-height: initial;
  .n-form-item-feedback-wrapper {
    min-height: initial;
  }
  .n-form-item-label {
    line-height: 16px;
  }
  .n-form-item-feedback-wrapper {
    padding-left: 12px !important;
  }
  .n-form-item-label__text {
    font-size: 16px;
  }
}

:deep(.n-form-item.n-form-item--left-labelled .n-form-item-label) {
  display: flex;
  align-items: end;
  text-align: initial;
  min-height: initial;
}

:deep(.strong-item .n-form-item-label__text) {
  font-weight: 500;
}

:deep(.input-form-item) {
  position: relative;
  .n-form-item-label {
    padding: 0;
  }
  .n-form-item-feedback-wrapper {
    color: #e50113;
    min-height: 12px;
  }
  .n-input-wrapper {
    padding-left: 0;
    padding-right: 0;
    border-bottom: 1px solid #e6e6e6;
  }
  .n-select {
    padding-left: 0;
    padding-right: 0;
    border-bottom: 1px solid #e6e6e6;
    .n-base-selection-input {
      padding-left: 0 !important;
    }
    .n-base-selection-overlay {
      padding-left: 0 !important;
      align-items: flex-start !important;
    }
  }
  .n-form-item-feedback-wrapper {
    padding: 0 !important;
  }
}
:deep(.n-collapse .n-collapse-item .n-collapse-item__header) {
  padding: 0;
}
:deep(.n-input__input-el) {
  height: initial;
  font-size: 14px;
  line-height: 14px;
  padding-bottom: 13px;
}
:deep(.n-input__placeholder) {
  font-size: 14px;
  align-items: flex-start !important;
}

:deep(.n-input--textarea) {
  .n-input-wrapper {
    padding-left: 8px;
    padding-right: 8px;
  }
  .n-input__textarea-el {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
  }
}
</style>
