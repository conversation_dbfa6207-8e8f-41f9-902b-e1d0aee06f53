<template>
  <!-- SEO 部分 -->
  <seo-data :pageData="pageData"></seo-data>
  <page-layout class="w-full bg-[#f5f3f3]">
    <template #search-category>
      <div class="w-full bg-white">
        <search-cate-card
          :categories="pageData.category"
          :cleareCate="pageData.cleareCate"
          class="mx-auto"
          searchWidth="1240px"
        ></search-cate-card>
      </div>
    </template>
    <template #main-content>
      <div class="cwidth mx-auto min-h-[50vh] mb-5">
        <n-split
          :default-size="pageData.type !== 'imgSearch' ? 0.2 : 0"
          :resize-trigger-size="0"
        >
          <template #1 v-if="pageData.type !== 'imgSearch'">
            <div
              class="bg-white h-full w-60 mx-2 p-2 filter-cate"
              :style="{ top: `${pageData.filterCateTop}rem` }"
            >
              <n-space vertical>
                <n-text class="font-medium">
                  <span>{{ authStore.i18n("cm_goods.filter") }}</span>
                  <span v-if="!!pageData.filterCateId"> (1) </span>
                </n-text>
                <div v-if="!!pageData.filterCateId">
                  <span @click="onFilterCateClose(pageData.filterCateId)">
                    <n-tag
                      type="warning"
                      closable
                      @close="onFilterCateClose(pageData.filterCateId)"
                    >
                      <n-ellipsis
                        style="max-width: 12rem"
                        class="cursor-pointer"
                      >
                        {{ pageData.filterCateName }}
                      </n-ellipsis>
                    </n-tag>
                  </span>
                </div>
                <div class="my-6"></div>
                <n-text class="font-medium">{{
                  authStore.i18n("cm_goods.category")
                }}</n-text>
                <div
                  v-for="item in pageData?.pageData?.categoryFilters"
                  class="pt-2"
                  :key="item.id"
                >
                  <n-grid :x-gap="12" :y-gap="8" :cols="12">
                    <n-grid-item :span="1">
                      <div @click="onFilterCateChecked(item)">
                        <n-radio
                          :value="item.id"
                          :checked="item.id == pageData.filterCateId"
                        >
                        </n-radio>
                      </div>
                    </n-grid-item>
                    <n-grid-item :span="11" class="px-1">
                      <div>
                        <div class="float-left">
                          <n-ellipsis style="max-width: 10rem">
                            {{ item.name }}
                          </n-ellipsis>
                        </div>
                        <div v-if="item?.children?.length > 0">
                          <div
                            class="float-right"
                            v-if="!pageData.filterCateShow[item.id]"
                          >
                            <icon-card
                              size="16"
                              name="ri:add-fill"
                              color="#000"
                              class="cursor-pointer"
                              @click="onFilterCateUnfold(item)"
                            ></icon-card>
                          </div>
                          <div class="float-right" v-else>
                            <icon-card
                              size="16"
                              name="iconoir:minus"
                              color="#000"
                              class="cursor-pointer"
                              @click="onFilterCateFold(item)"
                            ></icon-card>
                          </div>
                        </div>
                      </div>
                      <!-- 第二级 -->
                      <n-collapse-transition
                        :appear="false"
                        :show="!!pageData.filterCateShow[item.id]"
                      >
                        <div v-for="item2 in item.children" :key="item2.id">
                          <n-grid x-gap="12" :cols="12" class="pt-2">
                            <n-grid-item :span="1">
                              <div @click="onFilterCateChecked(item2)">
                                <n-radio
                                  :value="item2.id"
                                  :checked="item2.id == pageData.filterCateId"
                                >
                                </n-radio>
                              </div>
                            </n-grid-item>
                            <n-grid-item :span="11" class="px-1">
                              <div>
                                <div class="float-left">
                                  <n-ellipsis style="max-width: 9rem">
                                    {{ item2.name }}
                                  </n-ellipsis>
                                </div>
                                <div v-if="item2?.children?.length > 0">
                                  <div
                                    class="float-right"
                                    v-if="!pageData.filterCateShow[item2.id]"
                                  >
                                    <icon-card
                                      size="16"
                                      name="ri:add-fill"
                                      color="#000"
                                      class="cursor-pointer"
                                      @click="onFilterCateUnfold(item2)"
                                    ></icon-card>
                                  </div>
                                  <div class="float-right" v-else>
                                    <icon-card
                                      size="16"
                                      name="iconoir:minus"
                                      color="#000"
                                      class="cursor-pointer"
                                      @click="onFilterCateFold(item2)"
                                    ></icon-card>
                                  </div>
                                </div>
                              </div>
                              <!-- 第三级 -->
                              <n-collapse-transition
                                :show="!!pageData.filterCateShow[item.id]"
                              >
                                <div
                                  v-for="item3 in item2.children"
                                  :key="item3?.id"
                                >
                                  <n-grid x-gap="12" :cols="12" class="pt-2">
                                    <n-grid-item :span="1">
                                      <div @click="onFilterCateChecked(item3)">
                                        <n-radio
                                          :value="item3.id"
                                          :checked="
                                            item3.id == pageData.filterCateId
                                          "
                                        >
                                        </n-radio>
                                      </div>
                                    </n-grid-item>
                                    <n-grid-item :span="11" class="px-1">
                                      <div>
                                        <div class="float-left">
                                          <n-ellipsis style="max-width: 9rem">
                                            {{ item3.name }}
                                          </n-ellipsis>
                                        </div>
                                        <div v-if="item3?.children?.length > 0">
                                          <div
                                            class="float-right"
                                            v-if="
                                              !pageData.filterCateShow[item3.id]
                                            "
                                          >
                                            <icon-card
                                              size="16"
                                              name="ri:add-fill"
                                              color="#000"
                                              class="cursor-pointer"
                                              @click="onFilterCateUnfold(item3)"
                                            ></icon-card>
                                          </div>
                                          <div class="float-right" v-else>
                                            <icon-card
                                              size="16"
                                              name="iconoir:minus"
                                              color="#000"
                                              class="cursor-pointer"
                                              @click="onFilterCateFold(item3)"
                                            ></icon-card>
                                          </div>
                                        </div>
                                      </div>
                                    </n-grid-item>
                                  </n-grid>
                                </div>
                              </n-collapse-transition>
                            </n-grid-item>
                          </n-grid>
                        </div>
                      </n-collapse-transition>
                    </n-grid-item>
                  </n-grid>
                </div>
              </n-space>
            </div>
          </template>
          <template #2>
            <div class="my-2 sticky z-auto">
              <div v-if="currentBanner">
                <img
                  loading="lazy"
                  class="carousel-img pb-1 w-full"
                  :src="currentBanner"
                  referrerpolicy="no-referrer"
                />
              </div>
            </div>
            <div class="sticky z-auto">
              <template v-if="!pageData.loadingPage">
                <div class="mt-3 bg-white px-3 py-4">
                  <n-space
                    :style="{ gap: '0 20px' }"
                    class="flex flex-wrap items-center"
                  >
                    <div v-if="pageData.cateName">
                      <div class="bg-[#F6F6F6] px-2 py-1 h-[28px]">
                        <span
                          >{{ authStore.i18n("cm_goods.category") }}:
                          {{ pageData.cateName }}</span
                        >
                        <icon-card
                          name="mingcute:close-fill"
                          color="#666"
                          size="18"
                          class="ml-3 cursor-pointer"
                          @click="onClearCategory"
                        ></icon-card>
                      </div>
                      <!-- <span
                    class="cursor-pointer text-[#11A5E9] ml-2"
                    @click="onClearCategory"
                  >
                    {{ authStore.i18n("cm_goods.clearCategory") }}
                  </span> -->
                    </div>
                    <div v-if="pageData.keyword">
                      {{ authStore.i18n("cm_goods.showing") }}
                      {{ onfilterTotal(pageData.pageInfo.total) }}
                      {{ authStore.i18n("cm_goods.showingProduct") }} "{{
                        pageData.keyword
                      }}"
                    </div>
                    <div v-if="pageData.imageUrl">
                      <n-image
                        lazy
                        preview-disabled
                        object-fit="fill"
                        :src="pageData.imageUrl"
                        class="w-[60px] h-[60px] border border-[#e50113] rounded search-image-wrapper"
                        :img-props="{ referrerpolicy: 'no-referrer' }"
                      />
                    </div>
                    <template v-if="!pageData.imageUrl">
                      <n-checkbox-group
                        v-model:value="pageData.activeQueries"
                        @update:value="onGetGoodsListData(0)"
                      >
                        <n-space item-style="display: flex;">
                          <n-checkbox
                            v-for="query in queryData"
                            :label="query.value"
                            :value="query.key"
                            size="large"
                          />
                        </n-space>
                      </n-checkbox-group>
                    </template>

                    <!-- <div class="flex mt-2">
                  <div
                    class="flex items-center mr-8"
                    v-if="pageData.type !== 'imgSearch'"
                  >
                    <span>{{ authStore.i18n("cm_goods.minOrder") }}:</span>
                    <n-input-number
                      v-model:value="pageData.leMinBuyQuantity"
                      :show-button="false"
                      :placeholder="authStore.i18n('cm_goods.lessThan')"
                      class="w-26 ml-3 mr-2"
                      :precision="0"
                      :min="1"
                    />
                    <n-button @click="onMinBuyQuantity"
                      >{{ authStore.i18n("cm_goods.goSearch") }}
                    </n-button>
                  </div>
                  <div class="flex items-center">
                    <span> {{ authStore.i18n("cm_goods.price") }}</span>
                    <n-input-number
                      v-model:value="pageData.minPrice"
                      :show-button="false"
                      :placeholder="authStore.i18n('cm_goods.minPrice')"
                      class="w-20 mx-3"
                      :precision="2"
                      :min="0"
                    />
                    -
                    <n-input-number
                      v-model:value="pageData.maxPrice"
                      :show-button="false"
                      :placeholder="authStore.i18n('cm_goods.maxPrice')"
                      class="w-20 mx-3"
                      :precision="2"
                      :min="0"
                    />
                    <n-button @click="onPriceLimit"
                      >{{ authStore.i18n("cm_goods.goSearch") }}
                    </n-button>
                  </div>
                </div> -->
                  </n-space>
                </div>
                <div
                  class="relative w-full h-[56px] pl-[10px] pr-[24px] bg-[#F4F9FE] inline-flex items-center rounded-bl-[4px] rounded-br-[4px] overflow-hidden"
                >
                  <div
                    class="text-[14px] leading-[14px] text-[#6C839A] mr-[34px]"
                  >
                    {{ authStore.i18n("cm_goods.tellUsFindIt") }}
                  </div>
                  <a
                    target="_blank"
                    href="/goods/looking"
                    data-spm-box="button-find-goods-list-above"
                    class="py-[12px] px-[20px] text-center rounded-[500px] text-[#fff] bg-[#11263B] inline-flex text-[14px] leading-[14px] hover:bg-[#007FFF] transition-all duration-300 cursor-pointer"
                  >
                    {{ authStore.i18n("cm_goods.customSearch") }}
                  </a>
                  <img
                    :alt="authStore.i18n('cm_goods.customSearch')"
                    class="absolute bottom-[-70px] right-[24px]"
                    src="@/assets/icons/common/find-blue.svg"
                  />
                </div>
              </template>
              <div v-if="!pageData.loadingPage" class="mt-3">
                <div
                  v-if="!!pageData?.pageData?.goodsList?.length"
                  :data-spm-box="pageSource"
                  :data-spm-param="props.selector.selectorId"
                >
                  <!-- 商品列表 -->
                  <n-grid
                    :cols="
                      pageData.type !== 'imgSearch'
                        ? '4 s:4 m:4 l:4 xl:4 2xl:4'
                        : '5 s:5 m:5 l:5 xl:5 2xl:5'
                    "
                    :x-gap="12"
                    :y-gap="8"
                  >
                    <n-grid-item
                      v-for="(goods, index) in pageData?.pageData?.goodsList"
                      :key="goods?.goodsId"
                    >
                      <product-card
                        :goods="goods"
                        :goodsIndex="index"
                        :pageSource="pageSource"
                        @onOpenDetail="onOpenDetail"
                        @onUpdateGoodsId="onUpdateGoodsId"
                        @onUpdateLoading="onUpdateLoading"
                      ></product-card>
                    </n-grid-item>
                  </n-grid>
                  <div>
                    <img
                      loading="lazy"
                      v-if="pageData.isLoading"
                      src="@/assets/icons/loading.svg"
                      class="w-[50px] h-[50px] mx-auto"
                      referrerpolicy="no-referrer"
                    />
                  </div>
                  <n-pagination
                    v-if="pageData.type !== 'imgSearch'"
                    :page-slot="8"
                    :page-count="pageData.pageInfo.pages"
                    v-model:page="pageData.pageInfo.current"
                    v-model:page-size="pageData.pageInfo.size"
                    :on-update:page="onUpdatePageNo"
                    class="mt-8 text-center flex justify-center"
                  >
                    <template #prefix="{ pageCount }">
                      {{ authStore.i18n("cm_inquiry.total") }}
                      <span class="mx-[8px]">{{ pageCount }}</span>
                      Pages
                    </template>
                  </n-pagination>
                </div>
                <div
                  v-else
                  class="mt-[30px] flex flex-col items-center gap-[12px]"
                >
                  <img
                    loading="lazy"
                    src="@/assets/icons/common/no-data.svg"
                    class="w-[190px]"
                    referrerpolicy="no-referrer"
                  />
                  <div
                    class="max-w-[560px] text-[16px] leading-[28px] text-[#7F7F7F] text-center"
                  >
                    {{
                      pageData.padc
                        ? authStore.i18n("cm_search.activityExpired")
                        : authStore.i18n("cm_search.noData")
                    }}
                  </div>
                </div>
              </div>
              <div v-else class="flex justify-center items-center mt-[20%]">
                <n-spin stroke="#e50113" :show="pageData.loadingPage"> </n-spin>
              </div>
            </div>
          </template>
        </n-split>
      </div>
    </template>
  </page-layout>

  <div v-show="pageData.showLoading" class="loading-overlay">
    <n-spin stroke="#e50113" :show="pageData.showLoading"> </n-spin>
  </div>

  <goods-detail
    :visible="pageData.visible"
    :goods="pageData.currentGoods"
    @onCloseDetail="onCloseDetail"
    @onUpdateList="onUpdateList"
  ></goods-detail>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { recursiveQuery } from "@/utils/mixin";
import yiwuHotBanner from "@/assets/icons/yiwuHotPCBanner.jpg";
import mercadoHotBanner from "@/assets/icons/mercadoHotPCBanner.jpg";
import backSchoolHotBanner from "@/assets/icons/backSchoolHotBanner.jpg";
import winterHotSaleBanner from "@/assets/icons/goods/winter_hot_sale_banner.jpg";

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();
const authStore = useAuthStore();

const pageData = reactive(<any>{
  minPrice: null as number | null,
  maxPrice: null as number | null,
  categoryId: route?.query?.categoryId || route?.query?.cateId || "0",
  cateName: route?.query?.cateName || "",
  keyword: route.query.keyword || "",
  type: route.query.type || "",
  imageUrl: route.query.imageUrl || "",
  imageId: route.query.imageId || "",
  marketingRuleId: route.query.marketingRuleId || "",
  padc: route.query.padc || "",
  category: <any>[],
  goodsList: <any>[],
  currentGoods: {},
  visible: false,
  cleareCate: false,
  isLoading: false,
  noGoodsData: false,
  isDrawerFixed: false, // 是否显示侧边栏
  filterCateTop: 13,
  filterCateId: route.query.childCategoryId || "",
  filterCateName: route.query.childCateName || "",
  filterCateShow: {},
  loadingPage: true,
  showLoading: false,
  tempGoodsList: <any>[], //暂存不足一行的数据
  activeQueries: [],
  pageInfo: {
    current: 1,
    size: 20,
    total: 0,
    pages: 0,
  },
});

const queryData = [
  {
    key: "addToCartCount",
    value: authStore.i18n("cm_goods.sortByPopular"),
  },
];

const pageSource = computed(() => {
  if (route?.query?.activityId || route?.query?.padc) {
    return "activity-goods-list";
  } else if (route?.query?.tagId === "10000100") {
    return "tag-goods-mercado";
  } else if (route?.query?.tagId === "10000200") {
    return "tag-goods-yiwu";
  } else if (route?.query?.tagId === "20001600") {
    return "tag-goods-school";
  } else if (route?.query?.tagId === "20003800") {
    return "tag-goods-packing";
  } else if (route?.query?.tagId === "20004300") {
    return "tag-goods-bestselling";
  } else if (route?.query?.tagId) {
    return "tag-goods-list ";
  } else if (route?.query?.type === "recommendSearch") {
    return "recommend-goods-list";
  } else if (route?.query?.keyword) {
    return "search-goods-list";
  } else if (route?.query?.cateName) {
    return "category-goods-list";
  } else if (route?.query?.imageUrl) {
    return "image-search-list";
  }
  return "";
});

const bannerConfig = {
  "10000100": mercadoHotBanner,
  "10000200": yiwuHotBanner,
  "20001600": backSchoolHotBanner,
  "20004300": winterHotSaleBanner,
} as const;

const currentBanner = computed(() => {
  const tagId = route?.query?.tagId as keyof typeof bannerConfig;
  return bannerConfig[tagId];
});

initPageData();

onBeforeMount(() => {
  onPageData();
});

onMounted(() => {
  window.addEventListener("scroll", onScroll);
});
onBeforeUnmount(() => {
  window.removeEventListener("scroll", onScroll);
});

// 获取URL参数
async function initPageData() {
  if (route?.params?.id) {
    pageData.categoryId =
      route?.query?.categoryId || route?.query?.cateId || route?.params?.id;
    pageData.categoryId =
      pageData.categoryId === "all" ? "0" : pageData.categoryId;
  }
  if (route.query.pageNum) {
    pageData.pageNum = parseInt(route.query?.pageNum as string);
  }
  if (route.query.pageSize) {
    pageData.pageSize = parseInt(route.query?.pageSize as string);
  }
  if (route.query.keyword) {
    pageData.keyword = route.query.keyword;
  }
  if (route.query.min) {
    pageData.minPrice = parseFloat(route.query?.min as string);
  }
  if (route.query.max) {
    pageData.maxPrice = parseFloat(route.query?.max as string);
  }
  if (route.query.tagId) {
    pageData.tagIds = [route.query.tagId];
  }
}

async function onPageData() {
  pageData.loadingPage = true;
  const res: any = await useGoodsListPage({
    categoryId: pageData.categoryId,
    keyword: pageData.keyword,
    // minPrice: parseFloat(pageData.minPrice),
    // maxPrice: parseFloat(pageData.maxPrice),
    pageNo: pageData.pageInfo.current,
    pageSize: pageData.pageInfo.size,
    imageId: pageData.imageId,
    tagIds: pageData.tagIds,
    childCategoryId: pageData.filterCateId ?? null,
    marketingRuleId: pageData.marketingRuleId,
    padc: pageData.padc,
  });

  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    authStore.getCartList(res?.data.cartInfo);
    if (
      !pageData?.pageData?.goodsList?.length &&
      !!pageData.categoryId &&
      pageData.categoryId !== "0"
    ) {
      const enabled = await onCheckCategoryStatus();
      if (!enabled) {
        return navigateTo("/");
      }
    }
    onMarkCartItems(
      pageData?.pageData?.goodsList,
      pageData?.cartInfo?.goodsList
    );

    nuxtApp.$setResponseHeaders(pageData.seoData?.responseHeaders);
    pageData.category = res?.data?.categoryTree?.children;
    pageData.pageInfo.total = res?.data?.pageData?.page?.total;
    pageData.pageInfo.pages = res?.data?.pageData?.page?.pages;
    //  当前筛洗条件无 且商品列表没有数据时 显示找货
    if (
      !pageData.pageData?.goodsList?.length &&
      !pageData.minPrice &&
      pageData.minPrice != 0 &&
      !pageData.maxPrice &&
      pageData.maxPrice != 0 &&
      !pageData.leMinBuyQuantity &&
      !pageData.padc
    ) {
      router.push({
        path: "/goods/looking",
        query: {
          ...route.query,
        },
      });
      return;
    }

    await onGoodsHotFilter(pageData?.pageData?.categoryFilters);
  }
  pageData.loadingPage = false;
}

async function onCheckCategoryStatus() {
  const res: any = await useGetCategoryInfo({
    id: pageData.categoryId,
  });
  if (res?.result?.code === 200) {
    return res?.data?.enabled;
  }
  return false;
}

// 检查当前商品是否在购物车中 `goodsId` 不能唯一标识商品时，需额外比较 `padc` 字段
function onMarkCartItems(goodsList: any[], cartList: any[]) {
  if (!goodsList || !cartList) return;
  goodsList?.forEach((goods) => {
    const isInCart = cartList?.some((cart) => {
      if (cart.goodsId !== goods.goodsId) return false;
      if ("padc" in cart || "padc" in goods) {
        return cart?.padc === goods?.padc;
      }
      return true;
    });
    if (isInCart) {
      goods.selected = true;
    }
  });
}

async function onGoodsHotFilter(categoryFilters: any) {
  if (!!route.query.categoryId) {
    const target = categoryFilters?.find(
      (item: any) => item.id === route.query.categoryId
    );
    if (!!target) {
      await onFilterCateChecked(target);
    }
  }
}
function onfilterTotal(quantity: any) {
  if (quantity <= 100) {
    // 小于等于100的情况，展示具体的数量
    return quantity.toString();
  } else {
    // 超过100的情况，按照整百、整千、整万+展示
    var digitCount = Math.floor(Math.log10(quantity)) + 1;
    var base = Math.pow(10, digitCount - 1);
    var roundedValue = Math.floor(quantity / base) * base;

    if (digitCount <= 3) {
      return roundedValue.toString() + "+";
    } else {
      return roundedValue.toLocaleString() + "+";
    }
  }
}

// 起批量小于或等于填写值的商品搜索
function onMinBuyQuantity() {
  const remark = `起批数：${pageData.leMinBuyQuantity}；价格区间：${parseFloat(
    pageData.minPrice
  )}-${parseFloat(pageData.maxPrice)}`;
  window?.MyStat?.addPageEvent(`click_filter`, remark); // 埋点

  onGetGoodsListData();
}
// 价格区间搜索
function onPriceLimit() {
  const remark = `起批数：${pageData.leMinBuyQuantity}；价格区间：${parseFloat(
    pageData.minPrice
  )}-${parseFloat(pageData.maxPrice)}`;
  window?.MyStat?.addPageEvent(`click_filter`, remark); // 埋点

  onGetGoodsListData();
}
// 清除分类
function onClearCategory() {
  pageData.categoryId = "0";
  pageData.cateName = "";
  pageData.cleareCate = true;
  onGetGoodsListData();
}

async function onGetGoodsListData(type?: any) {
  if (type === "scroll" && pageData.noGoodsData) return;
  if (type === "scroll") {
    pageData.pageInfo.current++;
  } else if (type === "pagination") {
    pageData.loadingPage = true;
  } else if (!type) {
    pageData.pageInfo.current = 1;
  }
  const params: any = {
    categoryId: pageData.categoryId,
    keyword: pageData.keyword,
    pageNo: pageData.pageInfo.current,
    pageSize: pageData.pageInfo.size,
    // minPrice: parseFloat(pageData.minPrice),
    // maxPrice: parseFloat(pageData.maxPrice),
    // leMinBuyQuantity: pageData.leMinBuyQuantity,
    childCategoryId: pageData.filterCateId ?? null,
    imageId: pageData.imageId,
    tagIds: pageData.tagIds,
    marketingRuleId: pageData.marketingRuleId,
    padc: pageData.padc,
  };
  if (pageData.activeQueries.includes("addToCartCount")) {
    params.sortField = 42;
  }
  try {
    window?.MyStat.addPageEvent(
      "page_load",
      `加载页数：${pageData.pageInfo.current}`
    ); // 埋点

    const res: any = await useGoodsPageListData(params);
    pageData.pageData.categoryFilters = res?.data?.categoryFilters ?? [];
    if (
      !!pageData.filterCateId &&
      !recursiveQuery(res?.data?.categoryFilters, "id", pageData.filterCateId)
    ) {
      pageData.filterCateId = "";
    }
    if (type === "scroll" && (!res.data || !res.data?.goodsList?.length)) {
      pageData.noGoodsData = true;
      pageData.isLoading = false;
      return;
    }

    onMarkCartItems(res.data?.goodsList, authStore.$state.cartList);
    if (type === "scroll") {
      if (pageData.type === "imgSearch") {
        pageData.pageData.goodsList = pageData.pageData?.goodsList?.concat(
          res.data?.goodsList
        );
      } else {
        /**
         * 以图搜图不做去重处理
         * 滚动加载数据去重处理 不足一行的暂存起来不展示 下次接口请求将暂存数据合并，再次进行去重和展示处理
         * 注意：最后一组数据不足一行直接展示
         */
        const newGoodsList = res.data?.goodsList || [];
        const combinedGoodsList = [
          ...pageData.pageData.goodsList,
          ...pageData.tempGoodsList,
          ...newGoodsList,
        ];
        // 使用 goodsId 属性去重
        const uniqueGoodsList = combinedGoodsList.reduce((prev, current) => {
          if (!prev.some((item: any) => item.goodsId === current.goodsId)) {
            prev.push(current);
          }
          return prev;
        }, []);

        // 如果是最后一页且数据不足一行，直接展示所有数据
        if (res?.data?.page?.current >= res?.data?.page?.pages) {
          pageData.pageData.goodsList = uniqueGoodsList;
          pageData.tempGoodsList = [];
        } else {
          //计算当前展示的行数及每行需要的数量
          const itemsPerRow = 4;
          const fullRowsCount = Math.floor(
            uniqueGoodsList.length / itemsPerRow
          );
          const itemsToDisplay = fullRowsCount * itemsPerRow;
          // 将完整行的数据分离出来展示，不足一行的放入 tempGoodsList
          pageData.pageData.goodsList = uniqueGoodsList.slice(
            0,
            itemsToDisplay
          );
          pageData.tempGoodsList = uniqueGoodsList.slice(itemsToDisplay);
        }
      }

      pageData.pageInfo = res?.data?.page;
    } else if (type === "pagination") {
      pageData.pageInfo.total = res?.data?.page?.total;
      pageData.pageInfo.pages = res?.data?.page?.pages;
      pageData.pageData.goodsList = res.data?.goodsList || [];
    } else {
      pageData.pageData.goodsList = res.data?.goodsList || [];
      if (pageData.pageData.goodsList.length) {
        pageData.pageInfo = res?.data?.page;
      }
      //  当前筛洗条件无 且商品列表没有数据时 显示找货
      if (
        !pageData.pageData?.goodsList?.length &&
        !pageData.minPrice &&
        pageData.minPrice != 0 &&
        !pageData.maxPrice &&
        pageData.maxPrice != 0 &&
        !pageData.leMinBuyQuantity &&
        !pageData.padc
      ) {
        router.push({
          path: "/goods/looking",
          query: {
            ...route.query,
          },
        });
        return;
      }
    }
  } catch (error) {
    console.log("onScroll ~ error:", error);
  } finally {
    pageData.isLoading = false;
    pageData.loadingPage = false;
  }
}

async function getGoodsId(goods: any) {
  const res: any = await useGetGoods({ str: goods.sourceGoodsId });
  if (res?.result?.code === 200) {
    return res?.data;
  } else {
    showToast(authStore.i18n("cm_common_addGoodsError"));
  }
  return null;
}

async function onOpenDetail(e: any, goodsIndex: any) {
  pageData.showLoading = true;
  const goods = pageData.pageData.goodsList[goodsIndex];
  if (!goods.goodsId) {
    goods.goodsId = await getGoodsId(goods);
    if (!goods.goodsId) {
      pageData.showLoading = false;
      return;
    }
    pageData.pageData.goodsList[goodsIndex].goodsId = goods.goodsId;
  }
  pageData.visible = true;
  goods.spm = window.MyStat.getPageSPM(e);
  pageData.currentGoods = goods;
  pageData.showLoading = false;
}

function onUpdateGoodsId(goodsIndex, goodsId) {
  pageData.pageData.goodsList[goodsIndex].goodsId = goodsId;
}

function onUpdateLoading(val: any) {
  pageData.showLoading = val;
}

function onCloseDetail() {
  pageData.visible = false;
}
// 商品加购后 更新商品列表的选中状态
async function onUpdateList(goods: any) {
  pageData.pageData.goodsList.map((item: any) => {
    if (item.goodsId === goods.goodsId) {
      item.selected = true;
    }
  });
}

async function onScroll(e: any) {
  // y轴滚动
  if (e.deltaY !== 0) {
    if (pageData.type !== "imgSearch") {
      return;
    }
    const scrollY = Math.round((window?.scrollY / 16) * 100) / 100;
    pageData.filterCateTop = Math.round(13 - scrollY);
    if (scrollY >= 12.95) {
      pageData.filterCateTop = 0;
    }

    if (pageData.isLoading || pageData.noGoodsData) return;
    // 380 是底部信息栏的高度
    if (
      window.innerHeight + window.scrollY + 380 >=
      document.body.offsetHeight
    ) {
      pageData.isLoading = true;
      onGetGoodsListData("scroll");
    }
  }
}

async function onFilterCateChecked(item: any) {
  pageData.filterCateId = item.id;
  pageData.filterCateName = item.name;
  if (item?.children?.length > 0) {
    pageData.filterCateShow[item.id] = true;
  }

  await onGetGoodsListData();
}

function onFilterCateUnfold(item: any) {
  if (item?.children?.length > 0) {
    pageData.filterCateShow[item.id] = true;
  }
}

function onFilterCateFold(item: any) {
  delete pageData.filterCateShow[item.id];
}

function onFilterCateClose(id: any) {
  pageData.filterCateId = "";
  pageData.filterCateName = "";
  delete pageData.filterCateShow[id];
  onGetGoodsListData();
}

function onUpdatePageNo(page: number) {
  pageData.pageInfo.current = page;
  onGetGoodsListData("pagination");
  // 平滑滚动到顶部
  window.scrollTo({
    top: 0,
  });
}
</script>
<style scoped lang="scss">
.filter-cate {
  position: flex;
  top: 5rem;
  left: auto;
  background-color: #fff;
  color: rgb(2, 2, 2);
  padding: 10px;
  box-sizing: border-box;
  z-index: 0;
}
:deep(.search-image-wrapper) {
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
  }
}
.loading-overlay {
  position: fixed;
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
}
:deep(
    .n-pagination
      .n-pagination-item:not(
        .n-pagination-item--disabled
      ).n-pagination-item--active
  ) {
  border-radius: 100%;
  background: #e50113;
  color: #fff;
}
:deep(.n-pagination-prefix) {
  margin-right: 10px;
}
:deep(.n-pagination .n-pagination-item) {
  --n-item-text-color-hover: #e50113;
}
</style>
